{"name": "with-tailwind", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "check-types": "turbo run check-types", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@biomejs/biome": "2.0.5", "lint-staged": "^16.1.2", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "react-email": "4.0.17", "turbo": "^2.5.4", "ultracite": "5.0.26"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.2.15", "workspaces": ["apps/*", "packages/*"], "lint-staged": {"*.{js,jsx,ts,tsx,json,jsonc,css,scss,md,mdx}": ["npx ultracite format"]}, "dependencies": {"@react-email/components": "0.1.1", "react": "19.1.0", "react-dom": "19.1.0"}}