'use client';

import { motion, type Variants } from 'framer-motion';
import React from 'react';

/**
 * Layout Animation Variants
 * Based on Circle-temp's sophisticated animation patterns
 */

export const layoutAnimations = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.2, ease: 'easeOut' },
  },

  slideUp: {
    initial: { y: 20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: -20, opacity: 0 },
    transition: { duration: 0.3, ease: [0.4, 0, 0.2, 1] },
  },

  slideDown: {
    initial: { y: -20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: 20, opacity: 0 },
    transition: { duration: 0.3, ease: [0.4, 0, 0.2, 1] },
  },

  slideLeft: {
    initial: { x: 20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: -20, opacity: 0 },
    transition: { duration: 0.3, ease: [0.4, 0, 0.2, 1] },
  },

  slideRight: {
    initial: { x: -20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: 20, opacity: 0 },
    transition: { duration: 0.3, ease: [0.4, 0, 0.2, 1] },
  },

  scale: {
    initial: { scale: 0.95, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.95, opacity: 0 },
    transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] },
  },

  scaleUp: {
    initial: { scale: 0.8, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 1.1, opacity: 0 },
    transition: { duration: 0.3, ease: [0.4, 0, 0.2, 1] },
  },
} as const;

/**
 * Stagger Animation Variants
 * For animating lists and grids
 */
export const staggerAnimations = {
  container: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1,
      },
    },
    exit: {
      opacity: 0,
      transition: {
        staggerChildren: 0.02,
        staggerDirection: -1,
      },
    },
  } as Variants,

  item: {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.3,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    exit: {
      y: -10,
      opacity: 0,
      transition: {
        duration: 0.2,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  } as Variants,

  fastContainer: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.02,
        delayChildren: 0.05,
      },
    },
  } as Variants,

  fastItem: {
    hidden: { y: 10, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.2,
        ease: 'easeOut',
      },
    },
  } as Variants,
} as const;

/**
 * Layout Animation Components
 */

interface AnimatedContainerProps {
  children: React.ReactNode;
  animation?: keyof typeof layoutAnimations;
  className?: string;
  layoutId?: string;
  delay?: number;
}

export const AnimatedContainer: React.FC<AnimatedContainerProps> = ({
  children,
  animation = 'fadeIn',
  className,
  layoutId,
  delay = 0,
}) => {
  const animationConfig = layoutAnimations[animation];

  return (
    <motion.div
      animate={animationConfig.animate}
      className={className}
      exit={animationConfig.exit}
      initial={animationConfig.initial}
      layoutId={layoutId}
      transition={{
        ...animationConfig.transition,
        delay,
      }}
    >
      {children}
    </motion.div>
  );
};

interface StaggeredListProps {
  children: React.ReactNode;
  className?: string;
  fast?: boolean;
}

export const StaggeredList: React.FC<StaggeredListProps> = ({
  children,
  className,
  fast = false,
}) => {
  const containerVariant = fast
    ? staggerAnimations.fastContainer
    : staggerAnimations.container;
  const itemVariant = fast
    ? staggerAnimations.fastItem
    : staggerAnimations.item;

  return (
    <motion.div
      animate='visible'
      className={className}
      exit='exit'
      initial='hidden'
      variants={containerVariant}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariant}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

/**
 * Layout ID Animation Component
 * For smooth transitions between different layouts
 */
interface LayoutAnimationProps {
  children: React.ReactNode;
  layoutId: string;
  className?: string;
}

export const LayoutAnimation: React.FC<LayoutAnimationProps> = ({
  children,
  layoutId,
  className,
}) => (
  <motion.div
    className={className}
    layoutId={layoutId}
    transition={{
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1],
    }}
  >
    {children}
  </motion.div>
);

/**
 * Page Transition Component
 * For animating between different pages/routes
 */
interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className,
}) => (
  <motion.div
    animate={{ opacity: 1, y: 0 }}
    className={className}
    exit={{ opacity: 0, y: -20 }}
    initial={{ opacity: 0, y: 20 }}
    transition={{
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1],
    }}
  >
    {children}
  </motion.div>
);

/**
 * Conditional Animation Component
 * Animates only when condition changes
 */
interface ConditionalAnimationProps {
  children: React.ReactNode;
  show: boolean;
  animation?: keyof typeof layoutAnimations;
  className?: string;
}

export const ConditionalAnimation: React.FC<ConditionalAnimationProps> = ({
  children,
  show,
  animation = 'fadeIn',
  className,
}) => {
  const animationConfig = layoutAnimations[animation];

  return (
    <motion.div
      animate={show ? animationConfig.animate : animationConfig.initial}
      className={className}
      initial={false}
      transition={animationConfig.transition}
    >
      {children}
    </motion.div>
  );
};

/**
 * Animation Utilities
 */
export const animationUtils = {
  /**
   * Create a custom spring transition
   */
  spring: (stiffness = 300, damping = 30) => ({
    type: 'spring' as const,
    stiffness,
    damping,
  }),

  /**
   * Create a custom ease transition
   */
  ease: (duration = 0.3, ease: number[] = [0.4, 0, 0.2, 1]) => ({
    duration,
    ease,
  }),

  /**
   * Create a delayed transition
   */
  delayed: (delay = 0.1, duration = 0.3) => ({
    duration,
    delay,
    ease: [0.4, 0, 0.2, 1],
  }),
};
