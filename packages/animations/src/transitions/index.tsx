'use client';

import { AnimatePresence, motion, type Variants } from 'framer-motion';
import type React from 'react';

/**
 * Transition Components
 * For smooth transitions between different states and views
 */

/**
 * Modal Transition
 * Smooth modal enter/exit animations
 */
interface ModalTransitionProps {
  children: React.ReactNode;
  isOpen: boolean;
  className?: string;
}

export const ModalTransition: React.FC<ModalTransitionProps> = ({
  children,
  isOpen,
  className,
}) => (
  <AnimatePresence mode='wait'>
    {isOpen && (
      <motion.div
        animate={{ opacity: 1, scale: 1, y: 0 }}
        className={className}
        exit={{ opacity: 0, scale: 0.95, y: 10 }}
        initial={{ opacity: 0, scale: 0.95, y: 10 }}
        transition={{
          duration: 0.2,
          ease: [0.4, 0, 0.2, 1],
        }}
      >
        {children}
      </motion.div>
    )}
  </AnimatePresence>
);

/**
 * Backdrop Transition
 * Smooth backdrop fade in/out
 */
interface BackdropTransitionProps {
  children: React.ReactNode;
  isOpen: boolean;
  className?: string;
}

export const BackdropTransition: React.FC<BackdropTransitionProps> = ({
  children,
  isOpen,
  className,
}) => (
  <AnimatePresence>
    {isOpen && (
      <motion.div
        animate={{ opacity: 1 }}
        className={className}
        exit={{ opacity: 0 }}
        initial={{ opacity: 0 }}
        transition={{ duration: 0.15 }}
      >
        {children}
      </motion.div>
    )}
  </AnimatePresence>
);

/**
 * Slide Transition
 * Slide in/out from different directions
 */
interface SlideTransitionProps {
  children: React.ReactNode;
  isOpen: boolean;
  direction?: 'left' | 'right' | 'up' | 'down';
  className?: string;
}

export const SlideTransition: React.FC<SlideTransitionProps> = ({
  children,
  isOpen,
  direction = 'right',
  className,
}) => {
  const getSlideVariants = (): Variants => {
    const distance = 300;

    switch (direction) {
      case 'left':
        return {
          hidden: { x: -distance, opacity: 0 },
          visible: { x: 0, opacity: 1 },
          exit: { x: -distance, opacity: 0 },
        };
      case 'right':
        return {
          hidden: { x: distance, opacity: 0 },
          visible: { x: 0, opacity: 1 },
          exit: { x: distance, opacity: 0 },
        };
      case 'up':
        return {
          hidden: { y: -distance, opacity: 0 },
          visible: { y: 0, opacity: 1 },
          exit: { y: -distance, opacity: 0 },
        };
      case 'down':
        return {
          hidden: { y: distance, opacity: 0 },
          visible: { y: 0, opacity: 1 },
          exit: { y: distance, opacity: 0 },
        };
      default:
        return {
          hidden: { x: distance, opacity: 0 },
          visible: { x: 0, opacity: 1 },
          exit: { x: distance, opacity: 0 },
        };
    }
  };

  return (
    <AnimatePresence mode='wait'>
      {isOpen && (
        <motion.div
          animate='visible'
          className={className}
          exit='exit'
          initial='hidden'
          transition={{
            duration: 0.3,
            ease: [0.4, 0, 0.2, 1],
          }}
          variants={getSlideVariants()}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

/**
 * Collapse Transition
 * Smooth height collapse/expand
 */
interface CollapseTransitionProps {
  children: React.ReactNode;
  isOpen: boolean;
  className?: string;
}

export const CollapseTransition: React.FC<CollapseTransitionProps> = ({
  children,
  isOpen,
  className,
}) => (
  <motion.div
    animate={{
      height: isOpen ? 'auto' : 0,
      opacity: isOpen ? 1 : 0,
    }}
    className={className}
    initial={false}
    style={{ overflow: 'hidden' }}
    transition={{
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1],
    }}
  >
    {children}
  </motion.div>
);

/**
 * Tab Transition
 * Smooth transitions between tabs
 */
interface TabTransitionProps {
  children: React.ReactNode;
  activeKey: string;
  tabKey: string;
  className?: string;
}

export const TabTransition: React.FC<TabTransitionProps> = ({
  children,
  activeKey,
  tabKey,
  className,
}) => (
  <AnimatePresence mode='wait'>
    {activeKey === tabKey && (
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className={className}
        exit={{ opacity: 0, x: -20 }}
        initial={{ opacity: 0, x: 20 }}
        key={tabKey}
        transition={{
          duration: 0.2,
          ease: [0.4, 0, 0.2, 1],
        }}
      >
        {children}
      </motion.div>
    )}
  </AnimatePresence>
);

/**
 * Page Transition Wrapper
 * For transitioning between different pages/routes
 */
interface PageTransitionWrapperProps {
  children: React.ReactNode;
  className?: string;
  direction?: 'forward' | 'backward';
}

export const PageTransitionWrapper: React.FC<PageTransitionWrapperProps> = ({
  children,
  className,
  direction = 'forward',
}) => {
  const variants: Variants = {
    initial: {
      opacity: 0,
      x: direction === 'forward' ? 20 : -20,
    },
    animate: {
      opacity: 1,
      x: 0,
    },
    exit: {
      opacity: 0,
      x: direction === 'forward' ? -20 : 20,
    },
  };

  return (
    <motion.div
      animate='animate'
      className={className}
      exit='exit'
      initial='initial'
      transition={{
        duration: 0.3,
        ease: [0.4, 0, 0.2, 1],
      }}
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

/**
 * Notification Transition
 * For toast notifications and alerts
 */
interface NotificationTransitionProps {
  children: React.ReactNode;
  isVisible: boolean;
  position?: 'top' | 'bottom' | 'top-right' | 'bottom-right';
  className?: string;
}

export const NotificationTransition: React.FC<NotificationTransitionProps> = ({
  children,
  isVisible,
  position = 'top-right',
  className,
}) => {
  const getVariants = (): Variants => {
    switch (position) {
      case 'top':
        return {
          hidden: { opacity: 0, y: -50, scale: 0.95 },
          visible: { opacity: 1, y: 0, scale: 1 },
          exit: { opacity: 0, y: -50, scale: 0.95 },
        };
      case 'bottom':
        return {
          hidden: { opacity: 0, y: 50, scale: 0.95 },
          visible: { opacity: 1, y: 0, scale: 1 },
          exit: { opacity: 0, y: 50, scale: 0.95 },
        };
      case 'top-right':
        return {
          hidden: { opacity: 0, x: 300, scale: 0.95 },
          visible: { opacity: 1, x: 0, scale: 1 },
          exit: { opacity: 0, x: 300, scale: 0.95 },
        };
      case 'bottom-right':
        return {
          hidden: { opacity: 0, x: 300, scale: 0.95 },
          visible: { opacity: 1, x: 0, scale: 1 },
          exit: { opacity: 0, x: 300, scale: 0.95 },
        };
      default:
        return {
          hidden: { opacity: 0, x: 300, scale: 0.95 },
          visible: { opacity: 1, x: 0, scale: 1 },
          exit: { opacity: 0, x: 300, scale: 0.95 },
        };
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          animate='visible'
          className={className}
          exit='exit'
          initial='hidden'
          transition={{
            duration: 0.3,
            ease: [0.4, 0, 0.2, 1],
          }}
          variants={getVariants()}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

/**
 * Dropdown Transition
 * For dropdown menus and select components
 */
interface DropdownTransitionProps {
  children: React.ReactNode;
  isOpen: boolean;
  className?: string;
}

export const DropdownTransition: React.FC<DropdownTransitionProps> = ({
  children,
  isOpen,
  className,
}) => (
  <AnimatePresence>
    {isOpen && (
      <motion.div
        animate={{ opacity: 1, scale: 1, y: 0 }}
        className={className}
        exit={{ opacity: 0, scale: 0.95, y: -10 }}
        initial={{ opacity: 0, scale: 0.95, y: -10 }}
        transition={{
          duration: 0.15,
          ease: [0.4, 0, 0.2, 1],
        }}
      >
        {children}
      </motion.div>
    )}
  </AnimatePresence>
);

/**
 * Transition Utilities
 */
export const transitionUtils = {
  /**
   * Standard easing curves
   */
  easing: {
    easeOut: [0.4, 0, 0.2, 1],
    easeIn: [0.4, 0, 1, 1],
    easeInOut: [0.4, 0, 0.2, 1],
    sharp: [0.4, 0, 0.6, 1],
    standard: [0.4, 0, 0.2, 1],
  },

  /**
   * Standard durations
   */
  duration: {
    fast: 0.15,
    normal: 0.2,
    slow: 0.3,
    slower: 0.5,
  },

  /**
   * Create a spring transition
   */
  spring: (stiffness = 300, damping = 30) => ({
    type: 'spring' as const,
    stiffness,
    damping,
  }),

  /**
   * Create a tween transition
   */
  tween: (duration = 0.2, ease = [0.4, 0, 0.2, 1]) => ({
    duration,
    ease,
  }),
} as const;
