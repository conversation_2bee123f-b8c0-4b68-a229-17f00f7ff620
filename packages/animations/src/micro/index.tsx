'use client';

import { type MotionProps, motion } from 'framer-motion';
import React from 'react';

/**
 * Micro-interaction Components
 * Based on Circle-temp's sophisticated micro-interactions
 */

/**
 * Badge Stack Animation
 * Overlapped badges that separate on hover
 */
interface BadgeStackProps {
  children: React.ReactNode;
  className?: string;
}

export const BadgeStack: React.FC<BadgeStackProps> = ({
  children,
  className = '',
}) => (
  <motion.div
    className={`-space-x-5 transition-all duration-200 hover:space-x-1 lg:space-x-1 ${className}`}
    transition={{ duration: 0.15, ease: 'easeOut' }}
    whileHover={{ scale: 1.02 }}
  >
    {children}
  </motion.div>
);

/**
 * Hover Scale Animation
 * Subtle scale effect on hover
 */
interface HoverScaleProps extends MotionProps {
  children: React.ReactNode;
  scale?: number;
  className?: string;
}

export const HoverScale: React.FC<HoverScaleProps> = ({
  children,
  scale = 1.02,
  className,
  ...props
}) => (
  <motion.div
    className={className}
    transition={{ duration: 0.15, ease: 'easeOut' }}
    whileHover={{ scale }}
    whileTap={{ scale: scale * 0.98 }}
    {...props}
  >
    {children}
  </motion.div>
);

/**
 * Press Animation
 * Scale down effect on press/click
 */
interface PressAnimationProps extends MotionProps {
  children: React.ReactNode;
  scale?: number;
  className?: string;
}

export const PressAnimation: React.FC<PressAnimationProps> = ({
  children,
  scale = 0.95,
  className,
  ...props
}) => (
  <motion.div
    className={className}
    transition={{ duration: 0.1, ease: 'easeOut' }}
    whileTap={{ scale }}
    {...props}
  >
    {children}
  </motion.div>
);

/**
 * Floating Animation
 * Subtle floating effect for cards and elements
 */
interface FloatingProps {
  children: React.ReactNode;
  className?: string;
  intensity?: number;
}

export const Floating: React.FC<FloatingProps> = ({
  children,
  className,
  intensity = 2,
}) => (
  <motion.div
    animate={{
      y: [0, -intensity, 0],
    }}
    className={className}
    transition={{
      duration: 3,
      repeat: Number.POSITIVE_INFINITY,
      ease: 'easeInOut',
    }}
  >
    {children}
  </motion.div>
);

/**
 * Pulse Animation
 * Subtle pulse effect for notifications and highlights
 */
interface PulseProps {
  children: React.ReactNode;
  className?: string;
  scale?: [number, number];
  duration?: number;
}

export const Pulse: React.FC<PulseProps> = ({
  children,
  className,
  scale = [1, 1.05],
  duration = 2,
}) => (
  <motion.div
    animate={{
      scale,
    }}
    className={className}
    transition={{
      duration,
      repeat: Number.POSITIVE_INFINITY,
      repeatType: 'reverse',
      ease: 'easeInOut',
    }}
  >
    {children}
  </motion.div>
);

/**
 * Shimmer Animation
 * Loading shimmer effect
 */
interface ShimmerProps {
  className?: string;
  width?: string;
  height?: string;
}

export const Shimmer: React.FC<ShimmerProps> = ({
  className = '',
  width = '100%',
  height = '20px',
}) => (
  <motion.div
    animate={{
      backgroundPosition: ['200% 0', '-200% 0'],
    }}
    className={`rounded bg-gradient-to-r from-muted via-muted-foreground/20 to-muted ${className}`}
    style={{ width, height }}
    transition={{
      duration: 1.5,
      repeat: Number.POSITIVE_INFINITY,
      ease: 'linear',
    }}
  />
);

/**
 * Slide In Animation
 * Slide in from different directions
 */
interface SlideInProps {
  children: React.ReactNode;
  direction?: 'left' | 'right' | 'up' | 'down';
  distance?: number;
  className?: string;
  delay?: number;
}

export const SlideIn: React.FC<SlideInProps> = ({
  children,
  direction = 'up',
  distance = 20,
  className,
  delay = 0,
}) => {
  const getInitialPosition = () => {
    switch (direction) {
      case 'left':
        return { x: -distance, opacity: 0 };
      case 'right':
        return { x: distance, opacity: 0 };
      case 'up':
        return { y: distance, opacity: 0 };
      case 'down':
        return { y: -distance, opacity: 0 };
      default:
        return { y: distance, opacity: 0 };
    }
  };

  return (
    <motion.div
      animate={{ x: 0, y: 0, opacity: 1 }}
      className={className}
      initial={getInitialPosition()}
      transition={{
        duration: 0.3,
        delay,
        ease: [0.4, 0, 0.2, 1],
      }}
    >
      {children}
    </motion.div>
  );
};

/**
 * Rotate Animation
 * Rotation effect on hover or trigger
 */
interface RotateProps {
  children: React.ReactNode;
  degrees?: number;
  trigger?: 'hover' | 'tap' | 'always';
  className?: string;
}

export const Rotate: React.FC<RotateProps> = ({
  children,
  degrees = 180,
  trigger = 'hover',
  className,
}) => {
  const getAnimation = () => {
    switch (trigger) {
      case 'hover':
        return { whileHover: { rotate: degrees } };
      case 'tap':
        return { whileTap: { rotate: degrees } };
      case 'always':
        return {
          animate: { rotate: [0, degrees] },
          transition: {
            duration: 2,
            repeat: Number.POSITIVE_INFINITY,
            ease: 'linear',
          },
        };
      default:
        return {};
    }
  };

  return (
    <motion.div
      className={className}
      transition={{ duration: 0.2, ease: 'easeOut' }}
      {...getAnimation()}
    >
      {children}
    </motion.div>
  );
};

/**
 * Bounce Animation
 * Bounce effect for buttons and interactive elements
 */
interface BounceProps {
  children: React.ReactNode;
  className?: string;
  intensity?: number;
}

export const Bounce: React.FC<BounceProps> = ({
  children,
  className,
  intensity = 0.2,
}) => (
  <motion.div
    className={className}
    transition={{
      type: 'spring',
      stiffness: 400,
      damping: 10,
    }}
    whileHover={{
      y: -intensity * 10,
    }}
    whileTap={{
      y: 0,
    }}
  >
    {children}
  </motion.div>
);

/**
 * Fade In Up Animation
 * Combined fade and slide up effect
 */
interface FadeInUpProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  distance?: number;
}

export const FadeInUp: React.FC<FadeInUpProps> = ({
  children,
  className,
  delay = 0,
  distance = 20,
}) => (
  <motion.div
    animate={{ y: 0, opacity: 1 }}
    className={className}
    initial={{ y: distance, opacity: 0 }}
    transition={{
      duration: 0.3,
      delay,
      ease: [0.4, 0, 0.2, 1],
    }}
  >
    {children}
  </motion.div>
);

/**
 * Stagger Children Animation
 * Animate children with stagger effect
 */
interface StaggerChildrenProps {
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
}

export const StaggerChildren: React.FC<StaggerChildrenProps> = ({
  children,
  className,
  staggerDelay = 0.1,
}) => (
  <motion.div
    animate='visible'
    className={className}
    initial='hidden'
    variants={{
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: staggerDelay,
        },
      },
    }}
  >
    {React.Children.map(children, (child, index) => (
      <motion.div
        key={index}
        variants={{
          hidden: { y: 20, opacity: 0 },
          visible: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: [0.4, 0, 0.2, 1],
            },
          },
        }}
      >
        {child}
      </motion.div>
    ))}
  </motion.div>
);

/**
 * Micro-interaction Utilities
 */
export const microAnimations = {
  /**
   * Hover lift effect
   */
  hoverLift: {
    whileHover: { y: -2, scale: 1.02 },
    transition: { duration: 0.15, ease: 'easeOut' },
  },

  /**
   * Tap scale effect
   */
  tapScale: {
    whileTap: { scale: 0.95 },
    transition: { duration: 0.1, ease: 'easeOut' },
  },

  /**
   * Focus ring effect
   */
  focusRing: {
    whileFocus: {
      boxShadow: '0 0 0 2px rgba(59, 130, 246, 0.5)',
    },
    transition: { duration: 0.15 },
  },

  /**
   * Gentle bounce
   */
  gentleBounce: {
    whileHover: { y: -1 },
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 10,
    },
  },
} as const;
