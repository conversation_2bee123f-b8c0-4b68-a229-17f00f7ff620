import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Section,
  Text,
} from '@react-email/components';

const baseUrl = 'https://www.thehuefactory.co/';

const colors = {
  '100': '#ff4200',
  '200': '#d53700',
  '300': '#7f2100',
  '400': '#3e1000',
  '50': '#f8e1db',
};

const LaunchDay = ({ full_name = '<PERSON>' }) => (
  <Html>
    <Head />
    <Body style={main}>
      <Container
        style={{
          ...container,
          backgroundColor: colors['50'],
        }}
      >
        <Img
          alt='Email Header Image'
          height='auto'
          src={`${baseUrl}/email_hero.jpg`}
          width='100%'
        />
      </Container>
      <Container
        style={{
          margin: '0 auto',
          backgroundColor: colors['50'],
          alignItems: 'center',
          alignContent: 'center',
          textAlign: 'center',
        }}
      >
        <Section
          style={{
            backgroundColor: 'white',
            height: 20,
            borderTopLeftRadius: '16px',
            borderTopRightRadius: '16px',
          }}
        />
      </Container>
      <Container style={container}>
        <Heading style={h1}>Hey {full_name}, </Heading>
        <Text style={{ ...text, marginBottom: '24px' }}>
          We’re excited to announce that the wait is finally over. Thehuefactory
          website is officially live!
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          You can now visit us at{' '}
          <Link
            href='https://www.thehuefactory.co/'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            target='_blank'
          >
            {' '}
            www.thehuefactory.co{' '}
          </Link>{' '}
          to explore our services, projects, and everything we’ve been working
          on to bring creativity to life. We couldn’t have done it without your
          support, and we’re thrilled to have you along for the journey!
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Check it out, and let us know what you think. We’re eager to start
          collaborating and creating with you.
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Thank you for your patience and excitement – this is just the
          beginning!
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          You can reply this message or{' '}
          <Link
            href='https://www.thehuefactory.co/contact'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            target='_blank'
          >
            contact us
          </Link>{' '}
          with your questions.
        </Text>
      </Container>
      <Container
        style={{
          ...container,
          marginTop: '48px',
        }}
      >
        <Img
          alt="thehuefactory's Logo"
          height='42'
          src={`${baseUrl}/Logo_3dicon_orange.png`}
          width='42'
        />
        <Text style={{ ...footer, marginTop: '40px' }}>
          <Link
            href='https://www.thehuefactory.co/'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            target='_blank'
          >
            thehuefactory.co
          </Link>{' '}
          <br />
          The Creative Powerhouse.
          <br />
          Copyright © 2025 thehuefactory. All rights reserved.
        </Text>
      </Container>
    </Body>
  </Html>
);

export default LaunchDay;

const main = {
  backgroundColor: '#ffffff',
};

const container = {
  paddingLeft: '12px',
  paddingRight: '12px',
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
};

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
};

const text = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '24px 0',
};

const footer = {
  color: '#898989',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '22px',
  marginTop: '12px',
  marginBottom: '24px',
};
