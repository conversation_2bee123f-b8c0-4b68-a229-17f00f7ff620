import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { z } from 'zod';

const affiliateformSchema = z.object({
  first_name: z.string().min(2, {
    message: 'First Name must be at least 2 characters.',
  }),
  last_name: z.string().min(2, {
    message: 'Last Name must be at least 2 characters.',
  }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  number: z
    .string()
    .min(10, { message: 'Must be a valid mobile number' })
    .max(14, { message: 'Must be a valid mobile number' }),
  location: z.string().min(8, {
    message: 'Location must be at least 8 characters.',
  }),
  portfolio: z.string().url({
    message: 'Must be a link like https://www.thehuefactory.co/.',
  }),
  referer: z.string().optional(),
  message: z.string().min(10, {
    message: 'Message must be at least 10 characters.',
  }),
});

type JoinUsProps = z.infer<typeof affiliateformSchema>;

const baseUrl = 'https://www.thehuefactory.co/';

const colors = {
  '100': '#ff4200',
  '200': '#d53700',
  '300': '#7f2100',
  '400': '#3e1000',
};

export const JoinUsAffiliates = ({ first_name }: JoinUsProps) => (
  <Html>
    <Head />
    <Preview>Your Message Has Been Submited</Preview>
    <Body style={main}>
      <Container
        style={{
          ...container,
          backgroundColor: colors['100'],
        }}
      >
        <Img
          alt='Email Header Image'
          height='auto'
          src={`${baseUrl}/thehuefactory_hero.png`}
          width='100%'
        />
      </Container>
      <Container
        style={{
          margin: '0 auto',
          backgroundColor: colors['100'],
          alignItems: 'center',
          alignContent: 'center',
          textAlign: 'center',
        }}
      >
        <Section
          style={{
            backgroundColor: 'white',
            height: 20,
            // marginTop: '-4rem',
            borderTopLeftRadius: '16px',
            borderTopRightRadius: '16px',
          }}
        />
      </Container>
      <Container style={container}>
        <Heading style={h1}>Hey {first_name}, </Heading>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Your <span style={{ fontWeight: 'bold' }}> Affiliate</span> Role
          Application has been submitted. <br />
          You will be notified as soon as a decision is made on your
          Application.
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          You can reply this message with any extra questions or{' '}
          <Link
            href='https://www.thehuefactory.co/contact'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            target='_blank'
          >
            contact us
          </Link>{' '}
          with your questions.
        </Text>
      </Container>
      <Container
        style={{
          ...container,
          marginTop: '48px',
        }}
      >
        <Img
          alt="thehuefactory's Logo"
          height='42'
          src={`${baseUrl}/Logo_3dicon_orange.png`}
          width='42'
        />
        <Text style={{ ...footer, marginTop: '40px' }}>
          <Link
            href='https://www.thehuefactory.co/'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            target='_blank'
          >
            thehuefactory.co
          </Link>{' '}
          <br />
          The Creative Powerhouse.
          <br />
          Copyright © 2025 thehuefactory. All rights reserved.
        </Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: '#ffffff',
};

const container = {
  paddingLeft: '12px',
  paddingRight: '12px',
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
};

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
};

const text = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '24px 0',
};

const footer = {
  color: '#898989',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '22px',
  marginTop: '12px',
  marginBottom: '24px',
};
