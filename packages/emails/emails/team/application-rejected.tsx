import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import type React from 'react';

/**
 * Application Rejected Email Template
 * Sent to applicants when their application is rejected
 */

interface ApplicationRejectedEmailProps {
  applicantName: string;
  applicationId: string;
  role: string;
  rejectedAt: string;
  feedback?: string;
  reapplyUrl?: string;
  waitlistUrl?: string;
}

export const ApplicationRejectedEmail: React.FC<
  ApplicationRejectedEmailProps
> = ({
  applicantName,
  applicationId,
  role,
  rejectedAt,
  feedback,
  reapplyUrl = 'https://thehuefactory.co/join',
  waitlistUrl = 'https://thehuefactory.co/waitlist',
}) => {
  const previewText = `Update on your application for ${role} - ${applicationId}`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Header */}
          <Section style={header}>
            <Img
              alt='The Hue Factory'
              height='40'
              src='https://thehuefactory.co/logo.png'
              style={logo}
              width='40'
            />
            <Text style={headerText}>The Hue Factory</Text>
          </Section>

          {/* Main Content */}
          <Section style={content}>
            <Text style={title}>Application Update</Text>

            <Text style={greeting}>Hi {applicantName},</Text>

            <Text style={paragraph}>
              Thank you for your interest in joining The Hue Factory and for
              taking the time to apply for the <strong>{role}</strong> position.
              We appreciate the effort you put into your application.
            </Text>

            <Section style={applicationDetails}>
              <Text style={detailsTitle}>Application Details:</Text>
              <Text style={detail}>
                <strong>Application ID:</strong> {applicationId}
              </Text>
              <Text style={detail}>
                <strong>Role:</strong> {role}
              </Text>
              <Text style={detail}>
                <strong>Reviewed:</strong>{' '}
                {new Date(rejectedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </Text>
            </Section>

            <Text style={paragraph}>
              After careful consideration, we've decided not to move forward
              with your application at this time. This decision was not easy, as
              we received many strong applications from talented individuals.
            </Text>

            {feedback && (
              <Section style={feedbackContainer}>
                <Text style={feedbackTitle}>Feedback from our team:</Text>
                <Text style={feedbackText}>{feedback}</Text>
              </Section>
            )}

            <Text style={paragraph}>
              Please don't let this discourage you. We encourage you to continue
              developing your skills and consider applying again in the future
              when new opportunities arise.
            </Text>

            <Text style={sectionTitle}>Stay Connected:</Text>

            <Section style={buttonContainer}>
              <Button href={waitlistUrl} style={primaryButton}>
                Join Our Waitlist
              </Button>
            </Section>

            <Section style={buttonContainer}>
              <Button href={reapplyUrl} style={secondaryButton}>
                View Open Positions
              </Button>
            </Section>

            <Text style={paragraph}>
              We'll keep your information on file and notify you when similar
              positions become available. In the meantime, feel free to follow
              our work and connect with us on social media.
            </Text>

            <Text style={paragraph}>
              Thank you again for your interest in The Hue Factory. We wish you
              the best in your career journey!
            </Text>
          </Section>

          <Hr style={hr} />

          {/* Footer */}
          <Section style={footer}>
            <Text style={footerText}>
              Questions? Reply to this email or contact us at{' '}
              <Link href='mailto:<EMAIL>' style={link}>
                <EMAIL>
              </Link>
            </Text>

            <Text style={footerText}>
              <Link href='https://thehuefactory.co' style={link}>
                The Hue Factory
              </Link>{' '}
              • Building the future of creative collaboration
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  maxWidth: '600px',
};

const header = {
  padding: '32px 24px 24px',
  borderBottom: '1px solid #e6ebf1',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
};

const logo = {
  borderRadius: '8px',
};

const headerText = {
  fontSize: '18px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '0',
};

const content = {
  padding: '24px',
};

const title = {
  fontSize: '24px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '0 0 24px',
  textAlign: 'center' as const,
};

const greeting = {
  fontSize: '16px',
  color: '#1a1a1a',
  margin: '0 0 16px',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '24px',
  color: '#525252',
  margin: '0 0 16px',
};

const sectionTitle = {
  fontSize: '18px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '24px 0 16px',
};

const applicationDetails = {
  backgroundColor: '#f8fafc',
  border: '1px solid #e2e8f0',
  borderRadius: '8px',
  padding: '20px',
  margin: '24px 0',
};

const detailsTitle = {
  fontSize: '16px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '0 0 12px',
};

const detail = {
  fontSize: '14px',
  color: '#525252',
  margin: '0 0 8px',
};

const feedbackContainer = {
  backgroundColor: '#fef3c7',
  border: '1px solid #f59e0b',
  borderRadius: '8px',
  padding: '20px',
  margin: '24px 0',
};

const feedbackTitle = {
  fontSize: '16px',
  fontWeight: '600',
  color: '#92400e',
  margin: '0 0 12px',
};

const feedbackText = {
  fontSize: '15px',
  color: '#78350f',
  lineHeight: '22px',
  margin: '0',
  fontStyle: 'italic',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '16px 0',
};

const primaryButton = {
  backgroundColor: '#3b82f6',
  borderRadius: '8px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  border: 'none',
  cursor: 'pointer',
};

const secondaryButton = {
  backgroundColor: 'transparent',
  borderRadius: '8px',
  color: '#374151',
  fontSize: '14px',
  fontWeight: '500',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '10px 20px',
  border: '1px solid #d1d5db',
  cursor: 'pointer',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '20px 0',
};

const footer = {
  padding: '0 24px',
};

const footerText = {
  fontSize: '14px',
  color: '#8898aa',
  lineHeight: '20px',
  margin: '0 0 8px',
  textAlign: 'center' as const,
};

const link = {
  color: '#1a1a1a',
  textDecoration: 'underline',
};

export default ApplicationRejectedEmail;
