import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import type React from 'react';

/**
 * Application Approved Email Template
 * Sent to applicants when their application is approved
 */

interface ApplicationApprovedEmailProps {
  applicantName: string;
  applicationId: string;
  role: string;
  approvedAt: string;
  nextSteps?: string[];
  onboardingUrl?: string;
  ndaUrl?: string;
}

export const ApplicationApprovedEmail: React.FC<
  ApplicationApprovedEmailProps
> = ({
  applicantName,
  applicationId,
  role,
  approvedAt,
  nextSteps = [
    'Complete and sign the NDA',
    'Set up your team profile',
    'Join our Slack workspace',
    'Attend the next team meeting',
  ],
  onboardingUrl = 'https://teams.thehuefactory.co/onboarding',
  ndaUrl = 'https://teams.thehuefactory.co/nda',
}) => {
  const previewText = `Congratulations! Your application for ${role} has been approved`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Header */}
          <Section style={header}>
            <Img
              alt='The Hue Factory'
              height='40'
              src='https://thehuefactory.co/logo.png'
              style={logo}
              width='40'
            />
            <Text style={headerText}>The Hue Factory</Text>
          </Section>

          {/* Main Content */}
          <Section style={content}>
            <Text style={title}>🎉 Welcome to the Team!</Text>

            <Text style={greeting}>Hi {applicantName},</Text>

            <Text style={paragraph}>
              Congratulations! We're thrilled to inform you that your
              application for the <strong>{role}</strong> position has been{' '}
              <strong>approved</strong>. Welcome to The Hue Factory family!
            </Text>

            <Section style={applicationDetails}>
              <Text style={detailsTitle}>Application Summary:</Text>
              <Text style={detail}>
                <strong>Application ID:</strong> {applicationId}
              </Text>
              <Text style={detail}>
                <strong>Role:</strong> {role}
              </Text>
              <Text style={detail}>
                <strong>Approved:</strong>{' '}
                {new Date(approvedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </Section>

            <Text style={paragraph}>
              We were impressed by your skills, experience, and passion for
              creative collaboration. You're exactly the kind of person we want
              on our team!
            </Text>

            <Text style={sectionTitle}>Next Steps:</Text>
            <Section style={nextStepsContainer}>
              {nextSteps.map((step, index) => (
                <Text key={index} style={nextStepItem}>
                  {index + 1}. {step}
                </Text>
              ))}
            </Section>

            <Section style={buttonContainer}>
              <Button href={onboardingUrl} style={button}>
                Start Onboarding
              </Button>
            </Section>

            <Section style={buttonContainer}>
              <Button href={ndaUrl} style={secondaryButton}>
                Sign NDA
              </Button>
            </Section>

            <Text style={paragraph}>
              Our team lead will reach out to you within the next 24 hours to
              schedule your onboarding session and answer any questions you
              might have.
            </Text>

            <Text style={paragraph}>
              We're excited to have you aboard and can't wait to see what we'll
              create together!
            </Text>
          </Section>

          <Hr style={hr} />

          {/* Footer */}
          <Section style={footer}>
            <Text style={footerText}>
              Questions about onboarding? Contact us at{' '}
              <Link href='mailto:<EMAIL>' style={link}>
                <EMAIL>
              </Link>
            </Text>

            <Text style={footerText}>
              <Link href='https://thehuefactory.co' style={link}>
                The Hue Factory
              </Link>{' '}
              • Building the future of creative collaboration
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  maxWidth: '600px',
};

const header = {
  padding: '32px 24px 24px',
  borderBottom: '1px solid #e6ebf1',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
};

const logo = {
  borderRadius: '8px',
};

const headerText = {
  fontSize: '18px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '0',
};

const content = {
  padding: '24px',
};

const title = {
  fontSize: '28px',
  fontWeight: '700',
  color: '#1a1a1a',
  margin: '0 0 24px',
  textAlign: 'center' as const,
};

const greeting = {
  fontSize: '16px',
  color: '#1a1a1a',
  margin: '0 0 16px',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '24px',
  color: '#525252',
  margin: '0 0 16px',
};

const sectionTitle = {
  fontSize: '18px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '24px 0 16px',
};

const applicationDetails = {
  backgroundColor: '#f0fdf4',
  border: '1px solid #bbf7d0',
  borderRadius: '8px',
  padding: '20px',
  margin: '24px 0',
};

const detailsTitle = {
  fontSize: '16px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '0 0 12px',
};

const detail = {
  fontSize: '14px',
  color: '#525252',
  margin: '0 0 8px',
};

const nextStepsContainer = {
  backgroundColor: '#f8fafc',
  border: '1px solid #e2e8f0',
  borderRadius: '8px',
  padding: '20px',
  margin: '16px 0 24px',
};

const nextStepItem = {
  fontSize: '15px',
  color: '#374151',
  margin: '0 0 12px',
  lineHeight: '20px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '16px 0',
};

const button = {
  backgroundColor: '#10b981',
  borderRadius: '8px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 32px',
  border: 'none',
  cursor: 'pointer',
};

const secondaryButton = {
  backgroundColor: '#6b7280',
  borderRadius: '8px',
  color: '#ffffff',
  fontSize: '14px',
  fontWeight: '500',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '10px 24px',
  border: 'none',
  cursor: 'pointer',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '20px 0',
};

const footer = {
  padding: '0 24px',
};

const footerText = {
  fontSize: '14px',
  color: '#8898aa',
  lineHeight: '20px',
  margin: '0 0 8px',
  textAlign: 'center' as const,
};

const link = {
  color: '#1a1a1a',
  textDecoration: 'underline',
};

export default ApplicationApprovedEmail;
