import type { ReferalProposalType } from '.';

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[];

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      affiliate_proposals: {
        Row: {
          affiliate_proposal: ReferalProposalType | null;
          completed: boolean | null;
          created_at: string;
          id: number;
          is_approved: boolean | null;
          is_recieved: boolean | null;
          user_email: string | null;
          user_id: string | null;
        };
        Insert: {
          affiliate_proposal?: ReferalProposalType | null;
          completed?: boolean | null;
          created_at?: string;
          id?: number;
          is_approved?: boolean | null;
          is_recieved?: boolean | null;
          user_email?: string | null;
          user_id?: string | null;
        };
        Update: {
          affiliate_proposal?: ReferalProposalType | null;
          completed?: boolean | null;
          created_at?: string;
          id?: number;
          is_approved?: boolean | null;
          is_recieved?: boolean | null;
          user_email?: string | null;
          user_id?: string | null;
        };
        Relationships: [];
      };
      Emails: {
        Row: {
          created_at: string;
          email_address: string | null;
          id: number;
        };
        Insert: {
          created_at?: string;
          email_address?: string | null;
          id?: number;
        };
        Update: {
          created_at?: string;
          email_address?: string | null;
          id?: number;
        };
        Relationships: [];
      };
      JOIN_US_TABLE: {
        Row: {
          additional_info: string | null;
          approved: Database['public']['Enums']['is_accepted'];
          areas: string[] | null;
          available_days: string[] | null;
          created_at: string;
          dob: string | null;
          email: string;
          equipment: string | null;
          full_name: string | null;
          hours_per_week: string | null;
          id: number;
          interests: string | null;
          is_nda_signed: boolean | null;
          is_vol_form_submited: boolean;
          join_role: Database['public']['Enums']['Role Types'];
          location: string | null;
          message: string | null;
          nda_signed_date: string | null;
          newsletter: boolean | null;
          other_area: string | null;
          past_experience: string | null;
          phone: string | null;
          position: string | null;
          position_other: string | null;
          preferred_time: string | null;
          referer: string | null;
          resume_url: string | null;
          reviewed: Database['public']['Enums']['is_reviewed'];
          skills: string | null;
          user_id: string;
          why_join: string | null;
        };
        Insert: {
          additional_info?: string | null;
          approved?: Database['public']['Enums']['is_accepted'];
          areas?: string[] | null;
          available_days?: string[] | null;
          created_at?: string;
          dob?: string | null;
          email: string;
          equipment?: string | null;
          full_name?: string | null;
          hours_per_week?: string | null;
          id?: number;
          interests?: string | null;
          is_nda_signed?: boolean | null;
          is_vol_form_submited?: boolean;
          join_role?: Database['public']['Enums']['Role Types'];
          location?: string | null;
          message?: string | null;
          nda_signed_date?: string | null;
          newsletter?: boolean | null;
          other_area?: string | null;
          past_experience?: string | null;
          phone?: string | null;
          position?: string | null;
          position_other?: string | null;
          preferred_time?: string | null;
          referer?: string | null;
          resume_url?: string | null;
          reviewed?: Database['public']['Enums']['is_reviewed'];
          skills?: string | null;
          user_id?: string;
          why_join?: string | null;
        };
        Update: {
          additional_info?: string | null;
          approved?: Database['public']['Enums']['is_accepted'];
          areas?: string[] | null;
          available_days?: string[] | null;
          created_at?: string;
          dob?: string | null;
          email?: string;
          equipment?: string | null;
          full_name?: string | null;
          hours_per_week?: string | null;
          id?: number;
          interests?: string | null;
          is_nda_signed?: boolean | null;
          is_vol_form_submited?: boolean;
          join_role?: Database['public']['Enums']['Role Types'];
          location?: string | null;
          message?: string | null;
          nda_signed_date?: string | null;
          newsletter?: boolean | null;
          other_area?: string | null;
          past_experience?: string | null;
          phone?: string | null;
          position?: string | null;
          position_other?: string | null;
          preferred_time?: string | null;
          referer?: string | null;
          resume_url?: string | null;
          reviewed?: Database['public']['Enums']['is_reviewed'];
          skills?: string | null;
          user_id?: string;
          why_join?: string | null;
        };
        Relationships: [];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          email: string | null;
          full_name: string | null;
          id: string;
          role: Database['public']['Enums']['Role Types'];
          updated_at: string | null;
          username: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          email?: string | null;
          full_name?: string | null;
          id: string;
          role?: Database['public']['Enums']['Role Types'];
          updated_at?: string | null;
          username?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          email?: string | null;
          full_name?: string | null;
          id?: string;
          role?: Database['public']['Enums']['Role Types'];
          updated_at?: string | null;
          username?: string | null;
        };
        Relationships: [];
      };
      waitlists: {
        Row: {
          created_at: string;
          email_address: string;
          full_name: string | null;
          id: number;
          is_ld_email_sent: boolean | null;
        };
        Insert: {
          created_at?: string;
          email_address: string;
          full_name?: string | null;
          id?: number;
          is_ld_email_sent?: boolean | null;
        };
        Update: {
          created_at?: string;
          email_address?: string;
          full_name?: string | null;
          id?: number;
          is_ld_email_sent?: boolean | null;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      is_accepted: 'accepted' | 'reviewing' | 'notAccepted';
      is_reviewed: 'reviewed' | 'received' | 'notAccepted';
      'Role Types': 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type PublicSchema = Database[Extract<keyof Database, 'public'>];

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema['Tables'] & PublicSchema['Views'])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions['schema']]['Tables'] &
        Database[PublicTableNameOrOptions['schema']]['Views'])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions['schema']]['Tables'] &
      Database[PublicTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema['Tables'] &
        PublicSchema['Views'])
    ? (PublicSchema['Tables'] &
        PublicSchema['Views'])[PublicTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema['Tables']
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions['schema']]['Tables']
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema['Tables']
    ? PublicSchema['Tables'][PublicTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema['Tables']
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions['schema']]['Tables']
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema['Tables']
    ? PublicSchema['Tables'][PublicTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema['Enums']
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions['schema']]['Enums'][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema['Enums']
    ? PublicSchema['Enums'][PublicEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema['CompositeTypes']
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema['CompositeTypes']
    ? PublicSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;
