import { useUserStore } from '@thf/store/user';
import { useEffect } from 'react';
import { supabaseClient } from './client';

const supabase = supabaseClient();

export function useGetUser() {
  const { updateUser, user, removeUser } = useUserStore((state) => state);

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session?.user) {
        updateUser(session.user);
      } else {
        removeUser(null);
      }
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      if (!session) {
        removeUser(null);
      }
      updateUser(session?.user ?? null);
    });
  }, [updateUser, removeUser]);

  return { user };
}
