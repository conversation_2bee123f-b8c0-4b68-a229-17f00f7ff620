import { createBrowserClient } from '@supabase/ssr';
import type { Database } from '../db/types';

export const supabaseClient = () => {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  if (!url) {
    throw new Error(
      'NEXT_PUBLIC_SUPABASE_URL environment variable is not defined'
    );
  }
  if (!anonKey) {
    throw new Error(
      'NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable is not defined'
    );
  }
  return createBrowserClient<Database>(url, anonKey);
};
