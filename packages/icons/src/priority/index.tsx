import type React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
}

/**
 * No Priority Icon
 * Three dots indicating no priority set
 */
export const NoPriorityIcon = ({ className, ...props }: IconProps) => (
  <svg
    aria-label='No Priority'
    className={className}
    fill='currentColor'
    focusable='false'
    height='16'
    role='img'
    viewBox='0 0 16 16'
    width='16'
    {...props}
  >
    <rect height='1.5' opacity='0.9' rx='0.5' width='3' x='1.5' y='7.25' />
    <rect height='1.5' opacity='0.9' rx='0.5' width='3' x='6.5' y='7.25' />
    <rect height='1.5' opacity='0.9' rx='0.5' width='3' x='11.5' y='7.25' />
  </svg>
);

/**
 * Urgent Priority Icon
 * Filled square with exclamation mark
 */
export const UrgentPriorityIcon = ({ className, ...props }: IconProps) => (
  <svg
    aria-label='Urgent Priority'
    className={className}
    fill='currentColor'
    focusable='false'
    height='16'
    role='img'
    viewBox='0 0 16 16'
    width='16'
    {...props}
  >
    <path d='M3 1C1.91067 1 1 1.91067 1 3V13C1 14.0893 1.91067 15 3 15H13C14.0893 15 15 14.0893 15 13V3C15 1.91067 14.0893 1 13 1H3ZM7 4L9 4L8.75391 8.99836H7.25L7 4ZM9 11C9 11.5523 8.55228 12 8 12C7.44772 12 7 11.5523 7 11C7 10.4477 7.44772 10 8 10C8.55228 10 9 10.4477 9 11Z' />
  </svg>
);

/**
 * High Priority Icon
 * Three ascending bars, all filled
 */
export const HighPriorityIcon = ({ className, ...props }: IconProps) => (
  <svg
    aria-label='High Priority'
    className={className}
    fill='currentColor'
    focusable='false'
    height='16'
    role='img'
    viewBox='0 0 16 16'
    width='16'
    {...props}
  >
    <rect height='6' rx='1' width='3' x='1.5' y='8' />
    <rect height='9' rx='1' width='3' x='6.5' y='5' />
    <rect height='12' rx='1' width='3' x='11.5' y='2' />
  </svg>
);

/**
 * Medium Priority Icon
 * Three ascending bars, two filled, one faded
 */
export const MediumPriorityIcon = ({ className, ...props }: IconProps) => (
  <svg
    aria-label='Medium Priority'
    className={className}
    fill='currentColor'
    focusable='false'
    height='16'
    role='img'
    viewBox='0 0 16 16'
    width='16'
    {...props}
  >
    <rect height='6' rx='1' width='3' x='1.5' y='8' />
    <rect height='9' rx='1' width='3' x='6.5' y='5' />
    <rect fillOpacity='0.4' height='12' rx='1' width='3' x='11.5' y='2' />
  </svg>
);

/**
 * Low Priority Icon
 * Three ascending bars, one filled, two faded
 */
export const LowPriorityIcon = ({ className, ...props }: IconProps) => (
  <svg
    aria-label='Low Priority'
    className={className}
    fill='currentColor'
    focusable='false'
    height='16'
    role='img'
    viewBox='0 0 16 16'
    width='16'
    {...props}
  >
    <rect height='6' rx='1' width='3' x='1.5' y='8' />
    <rect fillOpacity='0.4' height='9' rx='1' width='3' x='6.5' y='5' />
    <rect fillOpacity='0.4' height='12' rx='1' width='3' x='11.5' y='2' />
  </svg>
);

// Priority configuration
export interface Priority {
  id: string;
  name: string;
  color: string;
  icon: React.FC<IconProps>;
  level: number;
}

export const priorities: Priority[] = [
  {
    id: 'no-priority',
    name: 'No priority',
    color: '#9ca3af',
    icon: NoPriorityIcon,
    level: 0,
  },
  {
    id: 'low',
    name: 'Low',
    color: '#6b7280',
    icon: LowPriorityIcon,
    level: 1,
  },
  {
    id: 'medium',
    name: 'Medium',
    color: '#facc15',
    icon: MediumPriorityIcon,
    level: 2,
  },
  {
    id: 'high',
    name: 'High',
    color: '#f97316',
    icon: HighPriorityIcon,
    level: 3,
  },
  {
    id: 'urgent',
    name: 'Urgent',
    color: '#ef4444',
    icon: UrgentPriorityIcon,
    level: 4,
  },
];

/**
 * Priority Icon Component with dynamic priority
 */
export const PriorityIcon: React.FC<{ priorityId: string } & IconProps> = ({
  priorityId,
  className,
  ...props
}) => {
  const currentPriority = priorities.find((p) => p.id === priorityId);

  if (!currentPriority) {
    return null;
  }

  const IconComponent = currentPriority.icon;
  return (
    <IconComponent
      className={className}
      style={{ color: currentPriority.color }}
      {...props}
    />
  );
};

/**
 * Priority Badge Component
 * Displays priority with icon and text
 */
export const PriorityBadge: React.FC<{
  priorityId: string;
  showText?: boolean;
  className?: string;
}> = ({ priorityId, showText = false, className = '' }) => {
  const currentPriority = priorities.find((p) => p.id === priorityId);

  if (!currentPriority) {
    return null;
  }

  return (
    <div className={`inline-flex items-center gap-1.5 ${className}`}>
      <PriorityIcon className='h-4 w-4' priorityId={priorityId} />
      {showText && (
        <span
          className='font-medium text-xs'
          style={{ color: currentPriority.color }}
        >
          {currentPriority.name}
        </span>
      )}
    </div>
  );
};

/**
 * Utility functions for priority management
 */
export const priorityUtils = {
  /**
   * Get priority by ID
   */
  getPriority: (id: string) => priorities.find((p) => p.id === id),

  /**
   * Get priority level (0-4)
   */
  getPriorityLevel: (id: string) =>
    priorities.find((p) => p.id === id)?.level ?? 0,

  /**
   * Compare priorities (for sorting)
   */
  comparePriorities: (a: string, b: string) => {
    const levelA = priorityUtils.getPriorityLevel(a);
    const levelB = priorityUtils.getPriorityLevel(b);
    return levelB - levelA; // Higher priority first
  },

  /**
   * Get next higher priority
   */
  getNextHigherPriority: (currentId: string) => {
    const current = priorityUtils.getPriority(currentId);
    if (!current || current.level >= 4) {
      return null;
    }
    return priorities.find((p) => p.level === current.level + 1);
  },

  /**
   * Get next lower priority
   */
  getNextLowerPriority: (currentId: string) => {
    const current = priorityUtils.getPriority(currentId);
    if (!current || current.level <= 0) {
      return null;
    }
    return priorities.find((p) => p.level === current.level - 1);
  },
};
