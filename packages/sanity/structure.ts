import type { StructureResolver } from 'sanity/structure';

// https://www.sanity.io/docs/structure-builder-cheat-sheet
export const structure: StructureResolver = (S) =>
  S.list()
    .title('Product Store')
    .items([
      S.documentTypeListItem('brandprojects').title('Projects'),
      S.divider(),
      ...S.documentTypeListItems().filter((item) => {
        const id = item.getId();
        return id !== undefined && !['brandprojects'].includes(id);
      }),
    ]);
