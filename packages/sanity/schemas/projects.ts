//@ts-nocheck
import { BookText } from 'lucide-react';
import { defineField, defineType } from 'sanity';

export const brandProjectTypes = defineType({
  name: 'brandprojects',
  title: 'Projects',
  icon: BookText,
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Title',
      description: 'Project Title',
      type: 'string',
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
      },
    }),
    defineField({
      name: 'subtitle',
      title: 'Sub-title',
      type: 'string',
    }),
    defineField({
      name: 'intro',
      title: 'Intro Content',
      type: 'text',
    }),
    defineField({
      name: 'projectlink',
      title: 'Project Link',
      type: 'url',
    }),
    defineField({
      name: 'ogImage',
      title: 'OG Image',
      description: 'this is the Og image or video',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'carouselLandscape',
      title: 'Carousel Landscape Image',
      description: 'this is the carousel image or video',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'carouselPortrait',
      title: 'carousel Portrait',
      description: 'this is the carousel portrait image or video',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'allProjectsLandscape',
      title: 'All Projects Landscape Image',
      description: 'this is the all Projects Landscape image or video',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'coverImage',
      title: 'Cover Image',
      description: 'this is the cover image or video',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'coverPortraitImage',
      title: 'Cover Portrait Image',
      description: 'this is the cover portrait image or video',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'row_one',
      title: 'First Row Image',
      description: 'Image or video for the first row',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'row_two',
      title: 'Row Two Image',
      description: 'Image or video for the second row',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'row_three_first_column',
      title: 'Row Three First Column Image',
      description: 'Image or video for the Third Row First Column',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'row_three_second_column',
      title: 'Row Three Second Column Image',
      description: 'Image or video for the Row Three Second Column',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'row_four',
      title: 'Row Four Image',
      description: 'Image or video for the first row',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'row_five_first_column',
      title: 'Row Five First Column Image/video',
      description: 'Image or video for the first row',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'row_five_second_column_one',
      title: 'Row Five Second Column First Image/video',
      description: 'Image or video for Row Five Second Column',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'row_five_second_column_two',
      title: 'Row Five Second Column Second Image/video',
      description: 'Image or video for Row Five Second Column',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'row_six',
      title: 'Row Six Image/video',
      description: 'Image or video for Row Six',
      type: 'cloudinary.asset',
    }),
    defineField({
      name: 'publishedAt',
      title: 'Published at',
      type: 'datetime',
    }),
    defineField({
      name: 'body',
      title: 'The Story',
      type: 'blockContent',
    }),
  ],

  preview: {
    select: {
      title: 'title',
      subtitle: 'subtitle',
      media: 'logoImageLight',
    },
  },
});
