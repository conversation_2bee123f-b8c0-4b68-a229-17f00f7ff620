import { ReactLenis } from '@thf/providers/lenis';
// import { Analytics } from '@vercel/analytics/react';
import { Navbar } from '../../components/layouts/navbar';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ReactLenis options={{ lerp: 0.1, duration: 1.5, syncTouch: true }} root>
      <Navbar />
      {children}
      {/* <Analytics /> */}
    </ReactLenis>
  );
}
