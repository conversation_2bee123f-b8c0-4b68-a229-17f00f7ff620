'use client';

import { buttonVariants } from '@thf/ui/components/button';
import { cn } from '@thf/ui/utils/cn';
import { MotionConfig, motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import useMeasure from 'react-use-measure';
import { Logo, Logo_3d_orange, Logo_3d_white } from '../../../lib/imgs';
import { navlinks } from '../../../lib/urls';
import { HumburgerMenu } from './humburger-menu';
import { NavMenu } from './menu';

export function Navbar() {
  const pathname = usePathname();
  const [navColor, setNavColor] = useState(false);
  const [navMenuActive, setNavMenuActive] = useState(false);
  const [contentRef] = useMeasure();

  const is_home = pathname === '/';
  const is_join = pathname === '/join';

  const homeLogo = is_home ? Logo_3d_white : Logo_3d_orange;

  const changeBackground = () => {
    if (window.scrollY >= 16) {
      setNavColor(true);
    } else {
      setNavColor(false);
    }
  };

  const handleNavMenu = () => {
    setNavMenuActive((n) => !n);
  };
  const handleHideNavMenu = () => {
    setNavMenuActive(() => false);
  };

  useEffect(() => {
    changeBackground();
    // adding the event when scroll change background
    window.addEventListener('scroll', changeBackground);
  });

  return (
    <nav className={cn('relative z-450 flex flex-col items-center')}>
      <NavMenu
        handleHideNavMenu={handleHideNavMenu}
        navColor={navColor}
        navMenuActive={navMenuActive}
      />
      <div
        className={cn('fixed top-0 z-450 flex w-full flex-col items-center')}
      >
        {/* desktop */}
        <div className={cn('flex w-full flex-col items-center')}>
          <div className='mx-auto grid w-full max-w-7xl grid-cols-2 items-center gap-10 p-4 sm:grid-cols-5'>
            {/* Logo */}
            <div className=' flex items-center justify-start'>
              <motion.div
                animate={{ opacity: navColor ? 0 : 1 }}
                className={cn('hidden md:flex md:animate-out')}
              >
                <Link
                  className='flex w-10 flex-col items-center justify-center'
                  href={'/'}
                  onClick={handleHideNavMenu}
                >
                  <Image alt='LogoSquare' className='md:w-9 ' src={homeLogo} />
                </Link>
              </motion.div>
              {/* <Link
                href={'/'}
                onClick={handleHideNavMenu}
                className='w-10 items-center md:hidden justify-center flex flex-col'
              >
                <Image
                  src={homeLogo}
                  alt='LogoSquare'
                  className='md:w-7 w-9 '
                />
              </Link> */}
            </div>
            {/* desktop nav links */}
            <div className='hidden w-full items-center justify-center space-x-4 sm:col-span-3 sm:flex '>
              <MotionConfig>
                <motion.div
                  animate={{
                    width: navColor ? '440px' : '280px',
                  }}
                  className='inline-flex w-full items-center justify-between overflow-hidden rounded-full border border-neutral-200 bg-white '
                  initial={false}
                  transition={{ type: 'tween', bounce: 0.2, duration: 0.1 }}
                >
                  <div
                    className='flex w-full items-center justify-between py-2 '
                    ref={contentRef}
                  >
                    <Link
                      className={cn(
                        navColor ? '' : 'hidden',
                        'ml-2 flex w-10 flex-col items-center justify-center'
                      )}
                      href={'/'}
                      onClick={handleHideNavMenu}
                    >
                      <Image alt='LogoSquare' className='w-6' src={Logo} />
                    </Link>

                    <div className='flex items-center space-x-2 px-4'>
                      {navlinks.map((l, i) => (
                        <motion.div className='' key={i}>
                          <Link
                            className={cn(
                              'text-center font-semibold text-neutral-500 text-sm uppercase transition-all duration-200 ease-linear hover:text-accent-100'
                            )}
                            href={l.url}
                            onClick={handleHideNavMenu}
                          >
                            {l.title}
                          </Link>
                        </motion.div>
                      ))}
                    </div>
                    {/* {navColor && ( */}
                    <motion.div animate={{ opacity: navColor ? 1 : 0 }}>
                      <Link
                        className={cn(
                          buttonVariants({
                            // size: 'sm',
                          }),
                          'mr-2 bg-accent-100 uppercase'
                        )}
                        href={'/join'}
                      >
                        Join Us
                      </Link>
                    </motion.div>
                    {/* )} */}
                  </div>
                </motion.div>
              </MotionConfig>
            </div>
            {/* contact and hamburger menu */}
            <div className='flex items-center justify-end space-x-4'>
              <div
                className={cn(
                  'flex flex-row items-center space-x-4 transition-all duration-200 '
                )}
              >
                <motion.div
                  animate={{ opacity: navColor ? 0 : 1 }}
                  className={cn('hidden md:flex md:animate-out')}
                >
                  <Link
                    className={cn(
                      buttonVariants(),
                      'uppercase',
                      is_home
                        ? 'bg-white text-neutral-500 hover:bg-black hover:text-white'
                        : 'bg-accent-100'
                    )}
                    href={'/join'}
                  >
                    Join Us
                  </Link>
                </motion.div>
                {/* <Link
                  href={'/join'}
                  className={cn(
                    buttonVariants(),
                    is_join ? 'hidden' : 'md:hidden'
                  )}
                >
                  Join Us
                </Link> */}
                <HumburgerMenu
                  className='sm:hidden '
                  handleNavMenu={handleNavMenu}
                  is_dark={is_join ? 'light' : 'dark'}
                  navMenuActive={navMenuActive}
                  navMenuColor={navColor}
                />
              </div>
            </div>
          </div>
        </div>
        {/* content toggle */}
      </div>
    </nav>
  );
}
