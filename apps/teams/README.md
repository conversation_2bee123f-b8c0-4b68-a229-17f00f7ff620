# Teams Application

The Hue Factory's team management and collaboration platform.


## Features

- **Real Data Integration**: Uses actual Supabase database with live data
- **Authentication & Authorization**: Role-based access control (Admin, Collaborator, Affiliate, Volunteer)
- **Application Management**: Complete workflow for team applications with real-time updates
- **Live Data Synchronization**: Real-time subscriptions for instant updates across all users
- **Email Notifications**: Automated email workflows for status changes (ready for integration)
- **Responsive Design**: Mobile-first design with sophisticated UI components
- **Dashboard Analytics**: Real team statistics and activity monitoring
- **Permission-based UI**: Different interfaces for different user roles

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **UI**: Custom design system with Framer Motion animations
- **Database**: Supabase with real-time subscriptions
- **Authentication**: Supabase Auth with custom role management
- **Email**: Resend with React Email templates
- **State Management**: Zustand for client state, direct Supabase calls for server state
- **Styling**: Tailwind CSS with OKLCH color system

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```

3. Configure your environment variables:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - `SUPABASE_SERVICE_ROLE_KEY`
   - `RESEND_API_KEY`
   - `NEXT_PUBLIC_APP_URL`
   - `NEXT_PUBLIC_MAIN_SITE_URL`

4. Run the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3001](http://localhost:3001) in your browser.

## Project Structure

```
apps/teams/
├── app/
│   ├── (auth)/           # Authentication pages
│   ├── (dashboard)/      # Main application pages
│   ├── api/             # API routes
│   └── layout.tsx       # Root layout
├── components/          # App-specific components
├── lib/                # Utility functions
└── middleware.ts       # Authentication middleware
```

## Package Dependencies

- `@thf/teams-ui`: UI components for team management
- `@thf/teams-auth`: Authentication providers and hooks
- `@thf/teams-db`: Database queries and mutations
- `@thf/teams-emails`: Email templates and workflows
- `@thf/ui`: Base UI component library
- `@thf/theme`: Theme system and styling
- `@thf/icons`: Icon components
- `@thf/animations`: Animation utilities

## Key Features

### Authentication
- Role-based access control (Admin, Collaborator, Affiliate, Volunteer)
- Permission-based UI rendering
- Protected routes with middleware
- Session management with Supabase

### Application Management
- Complete application lifecycle
- Status tracking and updates
- Priority management
- Bulk operations
- Real-time notifications

### Email Workflows
- Automated status change notifications
- Welcome emails for approved applications
- Rejection emails with feedback
- NDA reminders
- Weekly summaries

### Real-time Features
- Live application updates
- Real-time statistics
- Collaborative editing
- Instant notifications

## Development

### Adding New Features

1. Create UI components in `packages/teams-ui`
2. Add database queries/mutations in `packages/teams-db`
3. Implement email templates in `packages/teams-emails`
4. Create pages in `app/(dashboard)`
5. Add API routes in `app/api`

### Testing

Run the test suite:
```bash
npm run test
```

### Building

Build for production:
```bash
npm run build
```

## Deployment

The application is designed to be deployed on Vercel with Supabase as the backend.

1. Connect your repository to Vercel
2. Configure environment variables
3. Deploy

## Contributing

1. Create a feature branch
2. Make your changes
3. Add tests
4. Submit a pull request

## License

Private - The Hue Factory
